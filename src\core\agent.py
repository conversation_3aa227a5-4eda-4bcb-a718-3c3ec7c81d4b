"""
Main AI Agent - Core intelligence with function calling capabilities
"""
import asyncio
import json
import time
from typing import Dict, Any, <PERSON>, Optional, AsyncGenerator
from dataclasses import dataclass

from .llm_providers import create_llm_provider, LLMProvider
from .tool_executor import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>all, <PERSON>lResult, ExecutionMode
from .retry_logic import <PERSON><PERSON><PERSON>og<PERSON>, NETWORK_RETRY
from ..config.settings import ArienConfig
from ..config.prompts import SYSTEM_PROMPT, TOOL_DESCRIPTIONS


@dataclass
class AgentResponse:
    """Response from the AI agent"""
    content: str
    tool_calls: List[ToolCall]
    tool_results: List[ToolResult]
    execution_time: float
    success: bool
    error: Optional[str] = None


class ArienAgent:
    """Main AI Agent with function calling capabilities"""

    def __init__(self, config: ArienConfig):
        self.config = config
        self.llm_provider = create_llm_provider(config.llm)
        self.tool_executor = ToolExecutor()
        self.retry_logic = RetryLogic(NETWORK_RETRY)
        self.conversation_history: List[Dict[str, str]] = []

    async def process_message(self, user_message: str) -> AgentResponse:
        """Process user message and return response with tool execution"""
        start_time = time.time()

        try:
            # Add user message to conversation
            self.conversation_history.append({
                "role": "user",
                "content": user_message
            })

            # Get AI response with potential tool calls
            ai_response = await self.retry_logic.execute_with_retry(
                self._get_ai_response
            )

            # Parse tool calls if any
            tool_calls = self._parse_tool_calls(ai_response)
            tool_results = []

            # Execute tools if needed
            if tool_calls:
                tool_results = await self._execute_tools(tool_calls)

                # Get final response with tool results
                final_response = await self._get_final_response(tool_results)
                content = final_response.get("content", ai_response.get("content", ""))
            else:
                content = ai_response.get("content", "")

            # Add assistant response to conversation
            self.conversation_history.append({
                "role": "assistant",
                "content": content
            })

            execution_time = time.time() - start_time

            return AgentResponse(
                content=content,
                tool_calls=tool_calls,
                tool_results=tool_results,
                execution_time=execution_time,
                success=True
            )

        except Exception as e:
            execution_time = time.time() - start_time
            return AgentResponse(
                content=f"I encountered an error: {str(e)}",
                tool_calls=[],
                tool_results=[],
                execution_time=execution_time,
                success=False,
                error=str(e)
            )

    async def stream_response(self, user_message: str) -> AsyncGenerator[str, None]:
        """Stream AI response in real-time"""
        try:
            # Add user message to conversation
            self.conversation_history.append({
                "role": "user",
                "content": user_message
            })

            # Stream AI response
            async for chunk in self.llm_provider.stream_completion(
                self.conversation_history,
                tools=self._get_tool_definitions()
            ):
                yield chunk

        except Exception as e:
            yield f"Error: {str(e)}"

    async def _get_ai_response(self) -> Dict[str, Any]:
        """Get AI response from LLM provider"""
        return await self.llm_provider.chat_completion(
            self.conversation_history,
            tools=self._get_tool_definitions()
        )

    async def _execute_tools(self, tool_calls: List[ToolCall]) -> List[ToolResult]:
        """Execute tool calls"""
        if self.config.tools.parallel_execution and len(tool_calls) > 1:
            # Execute tools in parallel
            return await self.tool_executor.execute_parallel(tool_calls)
        else:
            # Execute tools sequentially
            return await self.tool_executor.execute_sequential(tool_calls)

    async def _get_final_response(self, tool_results: List[ToolResult]) -> Dict[str, Any]:
        """Get final AI response after tool execution"""
        # Add tool results to conversation
        tool_messages = []
        for result in tool_results:
            tool_messages.append({
                "role": "tool",
                "content": result.output,
                "tool_call_id": result.call_id or "unknown"
            })

        # Get final response
        messages = self.conversation_history + tool_messages
        return await self.llm_provider.chat_completion(messages)

    def _parse_tool_calls(self, ai_response: Dict[str, Any]) -> List[ToolCall]:
        """Parse tool calls from AI response"""
        tool_calls = []

        # Handle different response formats
        if "tool_calls" in ai_response:
            # OpenAI-style tool calls
            for call in ai_response["tool_calls"]:
                tool_calls.append(ToolCall(
                    name=call["function"]["name"],
                    arguments=json.loads(call["function"]["arguments"]),
                    call_id=call.get("id")
                ))
        elif "function_call" in ai_response:
            # Single function call
            call = ai_response["function_call"]
            tool_calls.append(ToolCall(
                name=call["name"],
                arguments=json.loads(call["arguments"])
            ))

        return tool_calls

    def _get_tool_definitions(self) -> List[Dict[str, Any]]:
        """Get tool definitions for LLM"""
        tools = []

        for tool_name, tool_desc in TOOL_DESCRIPTIONS.items():
            tool_def = {
                "type": "function",
                "function": {
                    "name": tool_name,
                    "description": tool_desc["description"],
                    "parameters": {
                        "type": "object",
                        "properties": {},
                        "required": []
                    }
                }
            }

            # Add parameters
            for param_name, param_desc in tool_desc.get("parameters", {}).items():
                tool_def["function"]["parameters"]["properties"][param_name] = {
                    "type": "string",
                    "description": param_desc
                }
                tool_def["function"]["parameters"]["required"].append(param_name)

            tools.append(tool_def)

        return tools

    def clear_conversation(self):
        """Clear conversation history"""
        self.conversation_history.clear()

    def get_conversation_summary(self) -> str:
        """Get a summary of the conversation"""
        if not self.conversation_history:
            return "No conversation yet."

        summary = f"Conversation with {len(self.conversation_history)} messages:\n"
        for i, msg in enumerate(self.conversation_history[-5:], 1):  # Last 5 messages
            role = msg["role"].title()
            content = msg["content"][:100] + "..." if len(msg["content"]) > 100 else msg["content"]
            summary += f"{i}. {role}: {content}\n"

        return summary


class ArienAgent:
    """Main AI agent with function calling capabilities"""
    
    def __init__(self, config: ArienConfig):
        self.config = config
        self.llm_provider: Optional[LLMProvider] = None
        self.tool_executor = ToolExecutor()
        self.retry_logic = RetryLogic(NETWORK_RETRY)
        self.conversation_history: List[Dict[str, str]] = []
        
        # Initialize LLM provider
        self._initialize_provider()
    
    def _initialize_provider(self):
        """Initialize the LLM provider"""
        try:
            self.llm_provider = create_llm_provider(self.config.llm)
        except Exception as e:
            print(f"Failed to initialize LLM provider: {e}")
            self.llm_provider = None
    
    async def process_message(self, user_message: str, stream: bool = True) -> AgentResponse:
        """
        Process user message and execute any required tools
        
        Args:
            user_message: User's input message
            stream: Whether to stream the response
            
        Returns:
            AgentResponse with content and tool results
        """
        start_time = time.time()
        
        if not self.llm_provider:
            return AgentResponse(
                content="❌ LLM provider not configured. Please run setup first.",
                tool_calls=[],
                tool_results=[],
                execution_time=0,
                success=False,
                error="LLM provider not configured"
            )
        
        try:
            # Add user message to conversation
            self.conversation_history.append({
                "role": "user",
                "content": user_message
            })
            
            # Get AI response
            if stream:
                response_content = await self._stream_ai_response()
            else:
                response_content = await self._get_ai_response()
            
            # Parse and execute tool calls
            tool_calls = self._extract_tool_calls(response_content)
            tool_results = []
            
            if tool_calls:
                tool_results = await self.tool_executor.execute_tools(
                    tool_calls, ExecutionMode.AUTO
                )
                
                # Add tool results to conversation and get follow-up response
                follow_up_content = await self._process_tool_results(
                    response_content, tool_results, stream
                )
                
                if follow_up_content:
                    response_content += "\n\n" + follow_up_content
            
            # Add assistant response to conversation
            self.conversation_history.append({
                "role": "assistant",
                "content": response_content
            })
            
            execution_time = time.time() - start_time
            
            return AgentResponse(
                content=response_content,
                tool_calls=tool_calls,
                tool_results=tool_results,
                execution_time=execution_time,
                success=True
            )
            
        except Exception as e:
            execution_time = time.time() - start_time
            
            return AgentResponse(
                content=f"❌ Error processing message: {str(e)}",
                tool_calls=[],
                tool_results=[],
                execution_time=execution_time,
                success=False,
                error=str(e)
            )
    
    async def _get_ai_response(self) -> str:
        """Get AI response without streaming"""
        try:
            response = await self.retry_logic.execute_with_retry(
                self.llm_provider.chat_completion,
                self.conversation_history,
                list(TOOL_DESCRIPTIONS.keys())
            )
            
            if "choices" in response and response["choices"]:
                choice = response["choices"][0]
                if "message" in choice:
                    return choice["message"].get("content", "")
                elif "text" in choice:
                    return choice["text"]
            elif "message" in response:
                return response["message"].get("content", "")
            
            return "No response generated"
            
        except Exception as e:
            raise Exception(f"AI response error: {str(e)}")
    
    async def _stream_ai_response(self) -> str:
        """Get AI response with streaming"""
        try:
            content = ""
            async for chunk in self.llm_provider.stream_completion(
                self.conversation_history,
                list(TOOL_DESCRIPTIONS.keys())
            ):
                content += chunk
                # Note: Actual streaming display is handled by the UI layer
            
            return content
            
        except Exception as e:
            raise Exception(f"AI streaming error: {str(e)}")
    
    def _extract_tool_calls(self, response_content: str) -> List[ToolCall]:
        """Extract tool calls from AI response"""
        # Use tool executor's parsing method
        return self.tool_executor.parse_tool_calls_from_response(response_content)
    
    async def _process_tool_results(self, original_response: str, 
                                   tool_results: List[ToolResult], 
                                   stream: bool = True) -> str:
        """Process tool results and get AI follow-up"""
        if not tool_results:
            return ""
        
        # Create tool results summary for AI
        results_summary = "Tool Execution Results:\n\n"
        
        for result in tool_results:
            results_summary += f"Tool: {result.tool_name}\n"
            results_summary += f"Success: {result.success}\n"
            results_summary += f"Execution Time: {result.execution_time:.2f}s\n"
            
            if result.success:
                # Don't show full output to AI, just summary
                output_preview = result.output[:500] + "..." if len(result.output) > 500 else result.output
                results_summary += f"Output: {output_preview}\n"
            else:
                results_summary += f"Error: {result.error or 'Unknown error'}\n"
            
            results_summary += "\n"
        
        # Add tool results to conversation
        self.conversation_history.append({
            "role": "system",
            "content": f"Tool execution completed. {results_summary}"
        })
        
        # Get AI follow-up response
        try:
            if stream:
                follow_up = await self._stream_ai_response()
            else:
                follow_up = await self._get_ai_response()
            
            return follow_up
            
        except Exception as e:
            return f"Error processing tool results: {str(e)}"
    
    def clear_conversation(self):
        """Clear conversation history"""
        self.conversation_history.clear()
    
    def get_conversation_summary(self) -> Dict[str, Any]:
        """Get summary of current conversation"""
        return {
            "message_count": len(self.conversation_history),
            "last_message": self.conversation_history[-1] if self.conversation_history else None,
            "conversation_length": sum(len(msg["content"]) for msg in self.conversation_history)
        }
    
    async def stream_response(self, user_message: str) -> AsyncGenerator[str, None]:
        """Stream AI response in real-time"""
        if not self.llm_provider:
            yield "❌ LLM provider not configured. Please run setup first."
            return
        
        try:
            # Add user message to conversation
            self.conversation_history.append({
                "role": "user",
                "content": user_message
            })
            
            # Stream AI response
            response_content = ""
            async for chunk in self.llm_provider.stream_completion(
                self.conversation_history,
                list(TOOL_DESCRIPTIONS.keys())
            ):
                response_content += chunk
                yield chunk
            
            # Process tool calls after streaming completes
            tool_calls = self._extract_tool_calls(response_content)
            
            if tool_calls:
                yield "\n\n🔧 Executing tools...\n"
                
                tool_results = await self.tool_executor.execute_tools(
                    tool_calls, ExecutionMode.AUTO
                )
                
                # Show tool results
                for result in tool_results:
                    if result.success:
                        yield f"\n✅ {result.tool_name}: Completed in {result.execution_time:.2f}s\n"
                    else:
                        yield f"\n❌ {result.tool_name}: Failed - {result.error}\n"
                
                # Get follow-up response
                follow_up_content = await self._process_tool_results(
                    response_content, tool_results, False
                )
                
                if follow_up_content:
                    yield "\n\n"
                    yield follow_up_content
                    response_content += "\n\n" + follow_up_content
            
            # Add final response to conversation
            self.conversation_history.append({
                "role": "assistant",
                "content": response_content
            })
            
        except Exception as e:
            yield f"\n\n❌ Error: {str(e)}"
